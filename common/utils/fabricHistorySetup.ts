import * as fabric from 'fabric';

// Make fabric available globally for fabric-history
if (typeof window !== 'undefined') {
  (window as any).fabric = fabric;

  // Import fabric-history after fabric is available
  try {
    require('fabric-history');
    console.log('fabric-history loaded successfully');
  } catch (error) {
    console.error('Failed to load fabric-history:', error);
  }
}

export { fabric };
