'use client'

import React, {
  useState, useEffect,
} from 'react';
import { Canvas } from 'fabric';
// Import fabric with history setup
import { fabric } from '@/common/utils/fabricHistorySetup';
import {
  Undo2,
  Redo2,
  ZoomIn,
  ZoomOut,
  Layers,
  HelpCircle,
} from 'lucide-react';
import { LayersPanel } from './LayersPanel';
import { HelpModal } from './HelpModal';

interface FloatingToolbarProps {
  canvas: Canvas | null;
  zoomLevel: number;
  onZoomChange: (zoom: number) => void;
  onFitToView: () => void;
}

export const FloatingToolbar = ({
  canvas,
  zoomLevel,
  onZoomChange,
  onFitToView,
}: FloatingToolbarProps) => {
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);
  const [showLayers, setShowLayers] = useState(false);
  const [showHelp, setShowHelp] = useState(false);

  // Initialize fabric-history tracking
  useEffect(() => {
    if (!canvas) {
      return;
    }

    // Check if fabric-history methods are available
    console.log('Canvas undo method available:', typeof canvas.undo === 'function');
    console.log('Canvas redo method available:', typeof canvas.redo === 'function');
    console.log('Canvas clearHistory method available:', typeof canvas.clearHistory === 'function');

    // Listen to fabric-history events to update button states
    const handleHistoryAppend = () => {
      console.log('History append event fired');
      setCanUndo(true);
      setCanRedo(false);
    };

    const handleHistoryUndo = () => {
      console.log('History undo event fired');
      // After undo, we can always redo (at least one step)
      setCanRedo(true);
      // We'll keep undo enabled optimistically - fabric-history will handle the actual state
      // If there are no more undo steps, the library will simply not perform the action
    };

    const handleHistoryRedo = () => {
      console.log('History redo event fired');
      // After redo, we can always undo (at least one step)
      setCanUndo(true);
      // We'll keep redo enabled optimistically - fabric-history will handle the actual state
    };

    const handleHistoryClear = () => {
      console.log('History clear event fired');
      setCanUndo(false);
      setCanRedo(false);
    };

    // Listen for regular canvas events that should trigger history
    const handleObjectAdded = () => {
      console.log('Object added to canvas');
      // Small delay to allow fabric-history to process
      setTimeout(() => {
        setCanUndo(true);
        setCanRedo(false);
      }, 100);
    };

    const handleObjectRemoved = () => {
      console.log('Object removed from canvas');
      // Small delay to allow fabric-history to process
      setTimeout(() => {
        setCanUndo(true);
        setCanRedo(false);
      }, 100);
    };

    const handleObjectModified = () => {
      console.log('Object modified on canvas');
      // Small delay to allow fabric-history to process
      setTimeout(() => {
        setCanUndo(true);
        setCanRedo(false);
      }, 100);
    };

    // Add event listeners for fabric-history events
    canvas.on('history:append', handleHistoryAppend);
    canvas.on('history:undo', handleHistoryUndo);
    canvas.on('history:redo', handleHistoryRedo);
    canvas.on('history:clear', handleHistoryClear);

    // Add fallback listeners for regular canvas events
    canvas.on('object:added', handleObjectAdded);
    canvas.on('object:removed', handleObjectRemoved);
    canvas.on('object:modified', handleObjectModified);

    return () => {
      canvas.off('history:append', handleHistoryAppend);
      canvas.off('history:undo', handleHistoryUndo);
      canvas.off('history:redo', handleHistoryRedo);
      canvas.off('history:clear', handleHistoryClear);
      canvas.off('object:added', handleObjectAdded);
      canvas.off('object:removed', handleObjectRemoved);
      canvas.off('object:modified', handleObjectModified);
    };
  }, [canvas]);

  const handleUndo = () => {
    console.log('Undo button clicked, canUndo:', canUndo, 'canvas:', !!canvas);
    if (!canvas || !canUndo) {
      console.log('Undo blocked - canvas:', !!canvas, 'canUndo:', canUndo);
      return;
    }

    // Check if undo method exists
    if (typeof canvas.undo === 'function') {
      console.log('Calling canvas.undo()');
      canvas.undo();
    } else {
      console.error('canvas.undo is not a function');
    }
  };

  const handleRedo = () => {
    console.log('Redo button clicked, canRedo:', canRedo, 'canvas:', !!canvas);
    if (!canvas || !canRedo) {
      console.log('Redo blocked - canvas:', !!canvas, 'canRedo:', canRedo);
      return;
    }

    // Check if redo method exists
    if (typeof canvas.redo === 'function') {
      console.log('Calling canvas.redo()');
      canvas.redo();
    } else {
      console.error('canvas.redo is not a function');
    }
  };

  const handleZoomIn = () => {
    const newZoom = Math.min(zoomLevel * 1.2, 3);
    onZoomChange(newZoom);
  };

  const handleZoomOut = () => {
    const newZoom = Math.max(zoomLevel / 1.2, 0.1);
    onZoomChange(newZoom);
  };

  const zoomPercentage = Math.round(zoomLevel * 100);

  return (
    <>
      {/* Floating Toolbar */}
      <div className="fixed bottom-6 right-6 z-40 flex items-center gap-1 bg-white/90 backdrop-blur-sm border border-gray-200 rounded-lg shadow-lg p-2">
        {/* Undo */}
        <button
          onClick={handleUndo}
          disabled={!canUndo}
          className="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          title="Undo"
        >
          <Undo2 size={16} className="text-gray-700" />
        </button>

        {/* Redo */}
        <button
          onClick={handleRedo}
          disabled={!canRedo}
          className="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          title="Redo"
        >
          <Redo2 size={16} className="text-gray-700" />
        </button>

        {/* Separator */}
        <div className="w-px h-6 bg-gray-300 mx-1" />

        {/* Zoom Out */}
        <button
          onClick={handleZoomOut}
          className="p-2 rounded hover:bg-gray-100 transition-colors"
          title="Zoom Out"
        >
          <ZoomOut size={16} className="text-gray-700" />
        </button>

        {/* Zoom Percentage */}
        <button
          onClick={onFitToView}
          className="px-3 py-1 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded transition-colors min-w-[60px]"
          title="Fit to View"
        >
          {zoomPercentage}%
        </button>

        {/* Zoom In */}
        <button
          onClick={handleZoomIn}
          className="p-2 rounded hover:bg-gray-100 transition-colors"
          title="Zoom In"
        >
          <ZoomIn size={16} className="text-gray-700" />
        </button>

        {/* Separator */}
        <div className="w-px h-6 bg-gray-300 mx-1" />

        {/* Layers */}
        <button
          onClick={() => setShowLayers(!showLayers)}
          className={`p-2 rounded transition-colors ${
            showLayers ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100 text-gray-700'
          }`}
          title="Layers"
        >
          <Layers size={16} />
        </button>

        {/* Help */}
        <button
          onClick={() => setShowHelp(true)}
          className="p-2 rounded hover:bg-gray-100 transition-colors"
          title="Help"
        >
          <HelpCircle size={16} className="text-gray-700" />
        </button>
      </div>

      {/* Layers Panel */}
      {showLayers && (
        <LayersPanel
          canvas={canvas}
          onClose={() => setShowLayers(false)}
        />
      )}

      {/* Help Modal */}
      {showHelp && (
        <HelpModal onClose={() => setShowHelp(false)} />
      )}
    </>
  );
};
