'use client'

import React, {
  useState, useRef, useEffect, useCallback,
} from 'react';
// Import fabric with history setup
import { fabric } from '@/common/utils/fabricHistorySetup';

// Type assertions for fabric.js v6 compatibility
type Canvas = any;
type FabricImage = any;
type IText = any;
const fabricAny = fabric as any;
import {
  CanvasSidebar, CanvasHeader,
} from '@/common/components/organisms';
import { FloatingToolbar } from './FloatingToolbar';
import { cn } from '@/common/utils/helpers';
import { PLATFORM_CANVAS_SIZES } from '@/common/constants';
import { useProjectContext } from '@/common/contexts/ProjectContext';
import { projectImageStorage } from '@/common/utils/projectImageStorage';
import toast from 'react-hot-toast';

interface CanvasEditorProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (imageUrl: string) => void;
  initialImage?: string;
  className?: string;
  agentId: string;
  planId: string;
  platform?: string;
}

export const CanvasEditor = ({
  isOpen,
  onClose,
  onSave,
  initialImage,
  className,
  agentId,
  planId,
  platform,
}: CanvasEditorProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const canvasContainerRef = useRef<HTMLDivElement>(null);
  const [fabricCanvas, setFabricCanvas] = useState<Canvas | null>(null);
  const [zoomLevel, setZoomLevel] = useState(1);
  const { activeProject } = useProjectContext();

  const handleZoomChange = useCallback((newZoom: number) => {
    setZoomLevel(newZoom);
  }, []);

  const calculateFitToViewZoom = useCallback((canvasWidth: number, canvasHeight: number) => {
    if (!containerRef.current) {
      return 1;
    }

    const container = containerRef.current;
    const containerWidth = container.clientWidth - 80;
    const containerHeight = container.clientHeight - 80;

    const scaleX = containerWidth / canvasWidth;
    const scaleY = containerHeight / canvasHeight;

    return Math.min(scaleX, scaleY, 1.2);
  }, []);

  const fitToView = useCallback(() => {
    if (!fabricCanvas || !containerRef.current) {
      return;
    }

    const platformKey = platform?.toLowerCase() as keyof typeof PLATFORM_CANVAS_SIZES;
    const canvasSize = PLATFORM_CANVAS_SIZES[platformKey] || PLATFORM_CANVAS_SIZES.default;
    const newZoom = calculateFitToViewZoom(canvasSize.width, canvasSize.height);

    setZoomLevel(newZoom);
    setTimeout(() => {
      if (containerRef.current) {
        const container = containerRef.current;
        const scrollableWidth = container.scrollWidth;
        const scrollableHeight = container.scrollHeight;
        const containerWidth = container.clientWidth;
        const containerHeight = container.clientHeight;

        const centerScrollLeft = (scrollableWidth - containerWidth) / 2;
        const centerScrollTop = (scrollableHeight - containerHeight) / 2;

        container.scrollTo({
          left: centerScrollLeft,
          top: centerScrollTop,
          behavior: 'smooth',
        });
      }
    }, 50);
  }, [fabricCanvas, platform, calculateFitToViewZoom, setZoomLevel]);

  
  useEffect(() => {
    if (fabricCanvas) {
      fabricCanvas.setZoom(zoomLevel);
      fabricCanvas.renderAll();
    }
  }, [zoomLevel, fabricCanvas]);

  useEffect(() => {
    if (fabricCanvas && isOpen) {
      setTimeout(() => {
        fitToView();
      }, 100);
    }
  }, [fabricCanvas, isOpen, fitToView]);

  useEffect(() => {
    if (!canvasRef.current || !isOpen) {
      return;
    }

    const platformKey = platform?.toLowerCase() as keyof typeof PLATFORM_CANVAS_SIZES;
    const canvasSize = PLATFORM_CANVAS_SIZES[platformKey] || PLATFORM_CANVAS_SIZES.default;
    const canvasWidth = canvasSize.width;
    const canvasHeight = canvasSize.height;

    const canvas = new fabricAny.Canvas(canvasRef.current, {
      width: canvasWidth,
      height: canvasHeight,
      backgroundColor: '#ffffff',
      selection: true,
      preserveObjectStacking: true,
    });

    setFabricCanvas(canvas);

    // Clear any existing history when canvas is initialized
    // Give more time for fabric-history to initialize
    setTimeout(() => {
      console.log('Clearing canvas history, clearHistory available:', typeof canvas.clearHistory === 'function');
      if (typeof canvas.clearHistory === 'function') {
        canvas.clearHistory();
      }
    }, 500);

    canvas.on('mouse:down', function (this: any, opt: any) {
      const evt = opt.e;
      if (evt.altKey === true) {
        this.isDragging = true;
        this.selection = false;
        this.lastPosX = evt.clientX;
        this.lastPosY = evt.clientY;
      }
    });

    canvas.on('mouse:move', function (this: any, opt: any) {
      if (this.isDragging) {
        const e = opt.e;
        const vpt = this.viewportTransform;
        vpt[4] += e.clientX - this.lastPosX;
        vpt[5] += e.clientY - this.lastPosY;
        this.requestRenderAll();
        this.lastPosX = e.clientX;
        this.lastPosY = e.clientY;
      }
    });

    canvas.on('mouse:up', function (this: any) {
      this.setViewportTransform(this.viewportTransform);
      this.isDragging = false;
      this.selection = true;
    });

    canvas.on('mouse:dblclick', function (opt: any) {
      const target = opt.target;
      if (target && (target.type === 'text' || target.type === 'i-text')) {
        const textObject = target as IText;
        canvas.setActiveObject(textObject);
        textObject.enterEditing();
        textObject.selectAll();
        canvas.renderAll();
      }
    });

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Delete') {
        const activeObjects = canvas.getActiveObjects();
        if (activeObjects.length) {
          activeObjects.forEach((obj: any) => canvas.remove(obj));
          canvas.discardActiveObject();
          canvas.renderAll();
        }
      }

      // Undo/Redo keyboard shortcuts
      if ((e.ctrlKey || e.metaKey) && e.key === 'z' && !e.shiftKey) {
        e.preventDefault();
        canvas.undo();
      }

      if ((e.ctrlKey || e.metaKey) && (e.key === 'y' || (e.key === 'z' && e.shiftKey))) {
        e.preventDefault();
        canvas.redo();
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    if (initialImage) {
      fabricAny.FabricImage.fromURL(initialImage, { crossOrigin: 'anonymous' }).then((img: any) => {
        const canvasWidth = canvas.width!;
        const canvasHeight = canvas.height!;
        const imgWidth = img.width!;
        const imgHeight = img.height!;

        const scaleX = canvasWidth / imgWidth;
        const scaleY = canvasHeight / imgHeight;
        const scale = Math.min(scaleX, scaleY);

        img.scale(scale);
        img.set({
          left: (canvasWidth - img.getScaledWidth()) / 2,
          top: (canvasHeight - img.getScaledHeight()) / 2,
        });
        canvas.add(img);
        canvas.renderAll();
      });
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      canvas.dispose();
    };
  }, [isOpen, initialImage, platform]);

  const handleSaveDesign = async () => {
    if (!fabricCanvas) {
      console.error('Canvas not available');
      return;
    }

    if (!activeProject?.project_id) {
      console.error('No active project');
      toast.error('No active project found');
      return;
    }

    try {
      const dataURL = fabricCanvas.toDataURL({
        format: 'png',
        quality: 1,
        multiplier: 1,
      });

      const response = await fetch(dataURL);
      const blob = await response.blob();
      const timestamp = Date.now();
      const fileName = `canvas-design-${timestamp}.png`;
      const file = new File([blob], fileName, { type: 'image/png' });

      const formData = new FormData();
      formData.append('image', file);
      formData.append('planId', planId || 'canvas-save');

      const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
      const uploadEndpoint = `${baseUrl}/agents/${agentId}/upload-canvas-image`;

      const uploadResponse = await fetch(uploadEndpoint, {
        method: 'POST',
        body: formData,
      });

      if (!uploadResponse.ok) {
        throw new Error('Failed to upload canvas image');
      }

      const uploadResult = await uploadResponse.json();

      if (uploadResult.success && uploadResult.filepath) {
        await projectImageStorage.addCreationImage(
          activeProject.project_id,
          agentId,
          uploadResult.filepath,
          fileName,
          planId,
          'Canvas Design',
        );

        window.dispatchEvent(new CustomEvent('projectImagesUpdated', {
          detail: { projectId: activeProject.project_id },
        }));

        onSave(uploadResult.filepath);
        toast.success('Canvas design saved successfully!');
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Error saving canvas design:', error);
      toast.error('Failed to save canvas design');
      onSave('');
    }
  };

  if (!isOpen) {
    return null;
  }
  return (
    <div className={cn(
      "fixed z-50 top-0 left-0 right-0 bottom-0 h-[calc(100vh)] bg-neutral-900 flex flex-col",
      className,
    )}>
      <CanvasHeader
        onSaveDesign={handleSaveDesign}
        canvas={fabricCanvas}
      />

      <div className="flex flex-1 min-h-0 flex-col md:flex-row">
        <div className="block">
          <CanvasSidebar
            canvas={fabricCanvas}
            agentId={agentId}
            planId={planId}
            containerRef={canvasContainerRef}
            zoomLevel={zoomLevel}
            onClose={onClose}
          />
        </div>
        <div className="flex-1 bg-neutral-800 flex flex-col overflow-hidden">
          <div ref={containerRef} className="flex-1 w-full overflow-auto scroll-smooth">
            <div
              className="flex items-center justify-center"
              style={{
                minHeight: `${Math.max(100, zoomLevel * 120)}%`,
                minWidth: `${Math.max(100, zoomLevel * 120)}%`,
                padding: `${Math.max(32, zoomLevel * 40)}px`,
              }}
            >
              <div ref={canvasContainerRef}
                className="bg-violets-are-blue/5 rounded-xl p-2 md:p-4 w-auto shadow-lg transition-transform duration-200"
                style={{
                  transform: `scale(${zoomLevel})`,
                  transformOrigin: 'center center',
                }}
              >
                <canvas
                  ref={canvasRef}
                  className="border border-gray-200 rounded block"
                />
              </div>
            </div>
          </div>
          <div className="p-2 md:p-4 text-center text-gray-500 text-xs">
            <div className="flex flex-col items-center gap-1">
              <p>Select layer for more options | Double-click text to edit inline | Delete key to remove selected objects</p>
            </div>
          </div>
        </div>
      </div>

      <FloatingToolbar
        canvas={fabricCanvas}
        zoomLevel={zoomLevel}
        onZoomChange={handleZoomChange}
        onFitToView={fitToView}
      />
    </div>
  );
};
